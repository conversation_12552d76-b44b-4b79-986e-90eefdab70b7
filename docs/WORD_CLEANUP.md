# Word Cleanup Documentation

## Overview

This document describes the word cleanup functionality that removes words without definitions from the database. This is useful for maintaining data quality and removing orphaned words that were created but never properly defined.

## Available Methods

### 1. Command Line Script

The primary method for cleaning up words is through the command line script.

#### Usage

```bash
# Dry run to see what would be deleted (recommended first step)
yarn cleanup:words-no-definitions --dry-run

# Dry run with specific language
yarn cleanup:words-no-definitions --dry-run --language=EN

# Dry run with custom batch size
yarn cleanup:words-no-definitions --dry-run --batch-size=50

# Actual cleanup (with confirmation prompt)
yarn cleanup:words-no-definitions

# Actual cleanup without confirmation prompt
yarn cleanup:words-no-definitions --force

# Cleanup only English words
yarn cleanup:words-no-definitions --language=EN --force

# Cleanup with custom batch size
yarn cleanup:words-no-definitions --batch-size=200 --force
```

#### Options

- `--dry-run`: Show what would be deleted without actually deleting (recommended)
- `--batch-size=N`: Process N words at a time (default: 100, max recommended: 1000)
- `--language=LANG`: Only process words in specific language (EN/VI)
- `--force`: Skip confirmation prompt

#### Examples

```bash
# Safe exploration - see what would be deleted
yarn cleanup:words-no-definitions --dry-run

# Check only Vietnamese words
yarn cleanup:words-no-definitions --dry-run --language=VI

# Small batch cleanup for testing
yarn cleanup:words-no-definitions --language=EN --batch-size=10

# Full cleanup with confirmation
yarn cleanup:words-no-definitions --batch-size=500
```

### 2. Admin API Endpoint

For programmatic access or admin interface integration.

#### Endpoints

**GET /api/admin/cleanup-words**
- Get statistics about words without definitions
- Requires ADMIN role

**POST /api/admin/cleanup-words**
- Perform cleanup or dry run
- Requires ADMIN role

#### Request Body (POST)

```json
{
  "language": "EN",        // Optional: "EN" or "VI"
  "batchSize": 100,        // Optional: 1-1000, default 100
  "dryRun": true          // Optional: default true
}
```

#### Response Examples

**GET Response:**
```json
{
  "success": true,
  "stats": {
    "total": 147287,
    "byLanguage": {
      "EN": 147287,
      "VI": 0
    },
    "hasWordsToCleanup": true
  }
}
```

**POST Response (Dry Run):**
```json
{
  "success": true,
  "dryRun": true,
  "preview": {
    "totalFound": 147287,
    "sampleWords": [
      {
        "id": "6b9535b9-ef03-4c78-8b82-846e3379267f",
        "term": "'s gravenhage",
        "language": "EN",
        "createdAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "wouldDelete": 147287,
    "batchSize": 100
  }
}
```

**POST Response (Actual Cleanup):**
```json
{
  "success": true,
  "dryRun": false,
  "result": {
    "deletedCount": 1000,
    "affectedCollections": 0,
    "language": "EN",
    "batchSize": 100
  }
}
```

### 3. WordService Methods

For programmatic access within the application.

```typescript
import { getWordService } from '@/backend/wire';

const wordService = getWordService();

// Find words without definitions
const wordsWithoutDefs = await wordService.findWordsWithoutDefinitions('EN', 100);

// Delete words without definitions
const result = await wordService.deleteWordsWithoutDefinitions('EN', 100);
console.log(`Deleted ${result.deletedCount} words`);
```

## What Gets Cleaned Up

When words without definitions are deleted, the following actions are performed:

1. **Word Records**: The word records themselves are deleted from the `Word` table
2. **LastSeenWord Entries**: Any spaced repetition tracking data is removed
3. **Collection References**: Words are removed from any collections that reference them
4. **Related Data**: Any WordNet data associated with the words is also deleted (cascade)

## Safety Features

### Batch Processing
- Words are processed in configurable batches to avoid database timeouts
- Default batch size is 100, maximum recommended is 1000
- Large datasets are automatically chunked to prevent memory issues

### Dry Run Mode
- Always use `--dry-run` first to see what would be deleted
- Shows sample words that would be affected
- Displays statistics about affected collections and user data

### Confirmation Prompts
- Interactive confirmation before actual deletion (unless `--force` is used)
- Clear statistics about what will be deleted

### Transaction Safety
- Database operations use transactions where possible
- Rollback on errors to maintain data consistency

## Performance Considerations

### Database Impact
- Large cleanup operations can be resource-intensive
- Use smaller batch sizes during peak hours
- Monitor database performance during cleanup

### Recommended Approach
1. Start with `--dry-run` to assess scope
2. Use smaller batch sizes for initial testing
3. Run during low-traffic periods for large cleanups
4. Monitor logs for any errors or issues

### Batch Size Guidelines
- **Small datasets** (< 1,000 words): batch-size=100
- **Medium datasets** (1,000-10,000 words): batch-size=200-500
- **Large datasets** (> 10,000 words): batch-size=500-1000

## Monitoring and Logging

### Script Output
The script provides detailed logging:
- Progress indicators for batch processing
- Statistics on words found and deleted
- Error reporting with specific details
- Summary statistics at completion

### Error Handling
- Graceful handling of database connection issues
- Detailed error messages for troubleshooting
- Partial success reporting (some batches succeed, others fail)

## Common Use Cases

### Regular Maintenance
```bash
# Weekly cleanup of orphaned words
yarn cleanup:words-no-definitions --dry-run
# Review output, then:
yarn cleanup:words-no-definitions --force
```

### Data Migration Cleanup
```bash
# After importing data, clean up incomplete records
yarn cleanup:words-no-definitions --language=EN --batch-size=500
```

### Development Environment Reset
```bash
# Clean up test data
yarn cleanup:words-no-definitions --force --batch-size=1000
```

## Troubleshooting

### Common Issues

**"Too many bind variables" Error**
- Reduce batch size: `--batch-size=100`
- The script automatically handles this for dry runs

**Database Connection Timeout**
- Reduce batch size
- Check database connection stability
- Run during low-traffic periods

**Permission Errors**
- Ensure database user has DELETE permissions
- Check that all related tables are accessible

### Recovery

If cleanup is interrupted:
1. Check the logs for the last successful batch
2. Re-run with `--dry-run` to see remaining work
3. Continue with normal cleanup process

The cleanup process is designed to be resumable - running it multiple times is safe.

## Best Practices

1. **Always dry run first**: Use `--dry-run` to understand the scope
2. **Start small**: Test with small batch sizes initially
3. **Monitor progress**: Watch logs for errors or performance issues
4. **Backup first**: Consider database backup before large cleanups
5. **Off-peak hours**: Run large cleanups during low-traffic periods
6. **Gradual approach**: For very large datasets, clean up in stages

## Integration with Admin Interface

The cleanup functionality can be integrated into admin dashboards using the provided API endpoints. This allows for:

- Real-time statistics display
- Web-based cleanup initiation
- Progress monitoring
- Historical cleanup logs

See the API documentation above for implementation details.
